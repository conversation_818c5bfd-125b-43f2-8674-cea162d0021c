[{"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/hacking.lua", "mt": 1748022010, "s": 867, "i": "k5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/keyminigame.lua", "mt": 1748022010, "s": 748, "i": "lJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/lockpick.lua", "mt": 1748022010, "s": 684, "i": "lZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/pinpad.lua", "mt": 1748022010, "s": 747, "i": "lpgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/quiz.lua", "mt": 1748022010, "s": 890, "i": "l5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/skillbar.lua", "mt": 1748022010, "s": 539, "i": "mJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/wordguess.lua", "mt": 1748022010, "s": 1048, "i": "mZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/client/wordscramble.lua", "mt": 1748022010, "s": 1353, "i": "mpgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/fxmanifest.lua", "mt": 1748022010, "s": 344, "i": "m5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/hacking.css", "mt": 1748022010, "s": 1255, "i": "nJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/keyminigame.css", "mt": 1748022010, "s": 665, "i": "nZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/lockpick.css", "mt": 1748022010, "s": 1559, "i": "npgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/pinpad.css", "mt": 1748022010, "s": 1206, "i": "n5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/quiz.css", "mt": 1748022010, "s": 7281, "i": "oJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/skillbar.css", "mt": 1748022010, "s": 204, "i": "oZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/tictactoe.css", "mt": 1748022010, "s": 0, "i": "opgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/wordguess.css", "mt": 1748022010, "s": 1492, "i": "o5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/css/wordscramble.css", "mt": 1748022010, "s": 1760, "i": "pJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/images/phone.png", "mt": 1748022010, "s": 8796, "i": "pZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/index.html", "mt": 1748022010, "s": 7833, "i": "ppgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/hacking.js", "mt": 1748022010, "s": 10218, "i": "p5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/keyminigame.js", "mt": 1748022010, "s": 4415, "i": "qJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/lockpick.js", "mt": 1748022010, "s": 7084, "i": "qZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/pinpad.js", "mt": 1748022010, "s": 2677, "i": "qpgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/quiz.js", "mt": 1748022010, "s": 7365, "i": "q5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/skillbar.js", "mt": 1748022010, "s": 5858, "i": "rJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/wordguess.js", "mt": 1748022010, "s": 3379, "i": "rZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/js/wordscramble.js", "mt": 1748022010, "s": 3074, "i": "rpgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/beep.ogg", "mt": 1748022010, "s": 6744, "i": "r5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/correct.ogg", "mt": 1748022010, "s": 10447, "i": "sJgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/fail.ogg", "mt": 1748022010, "s": 21845, "i": "sZgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/finish.ogg", "mt": 1748022010, "s": 13809, "i": "spgKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/start.ogg", "mt": 1748022010, "s": 28770, "i": "s5gKAAAAAwAAAAAAAAAAAA=="}, {"n": "L:/ACHRAF/Fivem/txData/QBCore_30B1AD.base/resources//[qb]/qb-minigames/html/sounds/wrong.ogg", "mt": 1748022010, "s": 7018, "i": "tJgKAAAAAwAAAAAAAAAAAA=="}]